<template>
  <view class="footerBtn-t flex flex-align-center">
    <view class="footerBtn-t-box flex flex-direction-column flex-align-center" @click="openRiderSet">
      <view class="icon-t">
        <u-icon name="car" size="40" color="#666"></u-icon>
      </view>
      <text class="r-text">外卖订单</text>
    </view>
    <view class="footerBtn-t-box flex flex-direction-column flex-align-center"
      @click="tabChange('/pages/pickUpIndex/index')">
      <view class="icon-t">
        <u-icon name="order" color="#666" size="40"></u-icon>
      </view>
      <text class="r-text">自提订单</text>
    </view>
    <!-- <view class="footerBtn-t-box flex flex-direction-column flex-align-center" @click="openShopSet">
      <view class="icon-t">
        <uni-icons type="shop" size="40" color="#666"></uni-icons>
      </view>
      <text class="r-text">门店管理</text>
    </view> -->
    <view class="footerBtn-t-box flex flex-direction-column flex-align-center" @click="tabChange(`/pages/goods/list?shopId=${UserInfo.shopId}`)">
				<view class="icon-t">
					<u-icon name="shopping-cart" color="#666" size="40"></u-icon>
				</view>
				<text class="r-text">商品管理</text>
			</view>
    <view class="footerBtn-t-box flex flex-direction-column flex-align-center" @click="openMineSet" style="background: azure;">
      <view class="icon-t">
        <uni-icons type="person" size="40" color="#666"></uni-icons>
      </view>
      <text class="r-text">个人中心</text>
    </view>
    <!-- <view class="footerBtn-t-cent" @click="handClickRefresh">
				<uni-icons type="loop" size="30" color="#ee8131"></uni-icons>
				<text class="c-text">刷新列表</text>
			</view>
			<view class="footerBtn-t-box flex flex-direction-column flex-align-center" @click="change(1)">
				<view class="icon-t">
					<u-icon name="order" color="#666" size="40"></u-icon>
				</view>
				<text class="r-text">自提订单</text>
			</view> -->

    <!-- 认证弹窗 -->
    <popup-one ref="PopupOne"></popup-one>
    <!-- 打开设置弹窗 -->
    <seting-pop ref="openSet"></seting-pop>
    <!-- 左菜单弹窗 -->
		<pop-page ref="popPage" :UserInfo="UserInfo"></pop-page>
  </view>
</template>

<script>
import { mapState } from 'vuex';
import PopupOne from '@/pages/index/components/PopupOne.vue'; // 底部认证
import SetingPop from '@/pages/index/components/setingPop.vue'; // 骑手设置弹窗
import PopPage from '@/pages/index/components/popup.vue'; // 左菜单弹窗
export default {
  computed: {
		...mapState(['UserInfo']),
	},
  components: {
    PopupOne,
    SetingPop,
    PopPage
  },
  methods: {
    openRiderSet () { //
      // this.$refs.openSet.init();
      uni.reLaunch({ url: '/pages/index/index' });
    },
    openShopSet () { //
      uni.$emit('RefreshUserInfo');
      this.$refs.openSet.init();
    },
    openMineSet () {
      // uni.$emit('RefreshUserInfo');
      // this.$refs.popPage.open(true); // 无论通过审核与否都可以弹窗侧栏
      this.reLaunch('/pages/persCenter/personalCenter')
    },
    tabChange (url) { // 页面切换
      // this.$refs.tabsSwiper.change(tab);
      // uni.$emit('RefreshOrderlist', 1);
      // uni.reLaunch({ url });
      uni.navigateTo({url});
    },
    openPopPage () { // 打开设置弹窗
      uni.$emit('RefreshUserInfo');
			this.$refs.popPage.open(true);
    },
    handClickRefresh () { }
  }
}
</script>

<style lang="scss" scoped>
.footerBtn-t {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 750rpx;
  height: 120rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #f1f1f1;
  z-index: 999;

  .footerBtn-t-box {
    min-width: 190rpx;
    padding-top: 11px;
    padding-bottom: 11px;

    .icon-t {
      min-height: 44rpx;
      max-height: 44rpx;
    }

    .r-text {
      color: $font-my-color-6;
      font-size: 24rpx;
    }
  }

  .footerBtn-t-cent {
    flex: 1;
    color: #ee8131;
    font-weight: bold;
    text-align: center;
    border-radius: 40rpx;
    overflow: hidden;
    border: 1rpx solid rgb(238, 129, 49);
    padding: 16rpx 0rpx;

    .c-text {
      margin-left: 10rpx;
      font-size: $font-my-size-32;
    }
  }
}

.popup-box-all {
  display: none;
}
</style>