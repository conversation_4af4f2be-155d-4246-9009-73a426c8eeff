<template>
	<view class="persCenter-cont">
		<view class="persCenter-bg">
			<view class="nav-bar"><uni-icons type="arrowleft" size="48" @click="$common.handBack"></uni-icons></view>
			<view class="persInfo flex flex-align-center">
				<image class="head-pic" :src="getFullFilePath(UserInfo.shopLogoImg) || '/static/image/head.png'" mode="widthFix"></image>
				<view class="name-text">
					<navigator url="/pages/myInfo/shopInfo" hover-class="none">
						<view class="flex flex-align-center">
							<text class="name-text-cont">{{ UserInfo.shopName || '待设置' }}</text>
							<uni-icons type="arrowright" size="40" :bold="true"></uni-icons>
						</view>
					</navigator>
					<view class="name-tirp">注册时间：{{ UserInfo.createTime
 ? UserInfo.createTime
.substring(0, 10) : '暂未认证' }}</view>
					<!-- <view class="name-tirp">
							<my-icon color="#939393" size="30">&#xe694;</my-icon>
							<text>认证时间</text>
						</view> -->
				</view>
			</view>
			<!-- <view class="persCenter-menu">
				<view class="persCenter-menu-ls flex flex-align-center justify-space-between">
					<view class="persCenter-menu-ls-name">
						<text>推荐指数</text>
						<text style="color: #ec1613; padding: 0 10rpx;">60</text>
						<uni-icons type="arrowright" color="#ec1613" size="30"></uni-icons>
					</view>
					<view class="persCenter-menu-ls-name">
						<text>接单权限</text>
						<text style="color: #ec1613; padding: 0 10rpx;">4</text>
						<uni-icons type="arrowright" color="#ec1613" size="30"></uni-icons>
					</view>
				</view>
				<my-line></my-line>
				<view class="persCenter-menu-ls-s flex flex-align-center justify-space-between">
					<view class="persCenter-menu-ls-s-name flex flex-align-center">
						<text class="persCenter-menu-ls-s-name-text">等级</text>
						<view class="bgcolor flex flex-align-center">
							<view class="mem-icon"><text class="mem-icon-name">V</text></view>
							<view class="mem-name flex flex-align-center">
								<text class="mem-name-text">{{ UserInfo.name || '暂无等级' }}</text>
							</view>
						</view>
					</view>
					<view class="persCenter-menu-ls-s-name flex flex-align-center">
						<text class="persCenter-menu-ls-s-name-text" style="margin-right: 20rpx;">服务分</text>
						<view class="persCenter-menu-ls-s-fu-box">
							<text>{{ 100 }}</text>
						</view>
					</view>
				</view>
			</view> -->
			<view class="persCenter-menu-two flex flex-align-center">
				<view class="persCenter-menu-two-ls flex flex-direction-column flex-align-center">
					<text class="weekMoneyNum">{{ UserInfo.todaySumMerchantIncome || 0 }}</text>
					<text class="weekMoneyName">每日收入(元)</text>
				</view>
				<view class="persCenter-menu-two-ls flex flex-direction-column flex-align-center">
					<text class="weekMoneyNum">{{ UserInfo.todayCountCancel || 0 }}</text>
					<text class="weekMoneyName">每日取消(单)</text>
				</view>
				<view class="persCenter-menu-two-ls flex flex-direction-column flex-align-center">
					<text class="weekMoneyNum">{{ UserInfo.todayCountPaid || 0 }}</text>
					<text class="weekMoneyName">每日接单(单)</text>
				</view>
			</view>
		</view>
		<view class="persCenter-menu-three">
			<!-- <navigator url="/pagesA/rankingList/rankingList" hover-class="none">
				<view class="persCenter-menu-three-ls flex flex-align-center justify-space-between">
					<text class="ls-title">接单排行</text>
					<view class="ls-label">
						<text>{{ UserInfo.ranking > 100 ? '还未排入前100名' : '第' + (UserInfo.ranking || '0' ) + '名' }}</text>
						<uni-icons type="arrowright" color="#999"></uni-icons>
					</view>
				</view>
			</navigator> -->
			<!-- <view class="persCenter-menu-three-ls flex flex-align-center justify-space-between">
				<text class="ls-title">接单评价</text>
				<view class="ls-label">
					<text>无</text>
					<uni-icons type="arrowright" color="#999"></uni-icons>
				</view>
			</view> -->
			<my-line :margin="[0, 0]"></my-line>
			<view class="persCenter-menu-three-ls flex flex-align-center justify-space-between">
				<text class="ls-title">实名认证</text>
				<view class="ls-label">
					<text v-if="UserInfo.auditStatus == '1'" style="color: #fd6211;">待审核</text>
					<text v-if="UserInfo.auditStatus == '2'" style="color: #19BE6B;">已认证</text>
					<uni-icons type="arrowright" color="#999"></uni-icons>
				</view>
			</view>
			<!-- <view class="persCenter-menu-three-ls">
				<navigator class="flex flex-align-center justify-space-between" url="/pages/persCenter/Up-health-cert" hover-class="none">
					<text class="ls-title">健康证</text>
					<uni-icons type="arrowright" color="#999"></uni-icons>
				</navigator>
			</view> -->
			<!-- <my-line :margin="[0, 0]"></my-line> -->
			<!-- <view class="persCenter-menu-three-ls flex flex-align-center justify-space-between">
					<text class="ls-title">车辆信息</text>
					<view class="ls-label">
						<text>未报备</text>
						<uni-icons type="arrowright"></uni-icons>
					</view>
				</view> -->
			<!-- <view class="persCenter-menu-three-ls flex flex-align-center justify-space-between">
				<text class="ls-title">装备餐箱</text>
				<view class="ls-label">
					<text>未报备</text>
					<uni-icons type="arrowright" color="#999"></uni-icons>
				</view>
			</view> -->
			<!-- <navigator url="/pages/persCenter/urgent-peple" hover-class="none">
				<view class="persCenter-menu-three-ls flex flex-align-center justify-space-between">
					<text class="ls-title">紧急联系人</text>
					<uni-icons type="arrowright" color="#999"></uni-icons>
				</view>
			</navigator> -->
		</view>
	</view>
</template>

<script>
import { mapState } from 'vuex';
import { getFullFilePath } from '../../common/method/common';
export default {
	data() {
		return {
			healthStateSty: {
				0: {
					color: '#999',
					title: '待审核'
				},
				1: {
					color: '#fdb81c',
					title: '待审核'
				},
				2: {
					color: '#19BE6B',
					title: '已认证'
				},
				3: {
					color: '#F56C6C',
					title: '已拒绝'
				}
			}
		};
	},
	onLoad() {
		this.getUserInfo();
	},
	computed: {
		...mapState(['UserInfo'])
	},
	methods: {
		getUserInfo() {
			uni.$emit('RefreshUserInfo');
		},
		getFullFilePath(url) {
			return getFullFilePath(url)
		}
	}
};
</script>

<style lang="scss" scoped>
.persCenter-cont {
	.persCenter-bg {
		background: url(~@/static/image/bgPic.png) no-repeat;
		background-size: 100% 100%;
		background-position: left top;
		padding: var(--status-bar-height) 32rpx 40rpx;

		.nav-bar {
			height: 88rpx;
			line-height: 88rpx;
		}

		.persInfo {
			.head-pic {
				display: inline-block;
				width: 120rpx;
				max-width: 120rpx;
				min-width: 120rpx;
				height: 120rpx;
				min-height: 120rpx;
				max-height: 120rpx;
				border-radius: 50%;
			}

			.name-text {
				margin-left: 20rpx;

				.name-text-cont {
					display: inline-block;
					font-size: $font-my-size-52;
					font-weight: bold;
				}

				.name-tirp {
					font-size: $font-my-size-24;
					color: $font-my-color-6;
				}
			}
		}

		.persCenter-menu {
			box-shadow: 0 0 10rpx 0rpx rgba(0, 0, 0, 0.1);
			padding: 30rpx 32rpx;
			margin-top: 30rpx;
			border-radius: 10rpx;

			.persCenter-menu-ls {
				padding-bottom: 40rpx;

				.persCenter-menu-ls-name {
					font-weight: bold;
					color: $font-my-color-3;
					font-size: $font-my-size-34;
				}
			}

			.persCenter-menu-ls-s {
				padding-top: 40rpx;

				.persCenter-menu-ls-s-name {
					.persCenter-menu-ls-s-name-text {
						font-size: $font-my-size-30;
						color: $font-my-color-6;
						margin-right: 40rpx;

						// &:last-child() {
						// 	margin-right: 20rpx;
						// }
					}

					.bgcolor {
						background: linear-gradient(135deg, #ee8131 10%, #ec1613);

						.mem-icon {
							width: 30rpx;
							height: 30rpx;
							line-height: 30rpx;
							text-align: center;
							background: #ec1613;
							box-shadow: 2rpx -2rpx 0 0 rgba(255, 255, 255, 1);
							transform: translate3d(-10rpx, 0, 10rpx) rotate(45deg);
							margin-left: -7rpx;

							.mem-icon-name {
								display: inline-block;
								font-size: $font-my-size-24;
								color: #fff;
								font-weight: bold;
								transform: rotate(-45deg);
							}
						}

						.mem-name {
							min-height: 30rpx;
							min-width: 100rpx;
							background: linear-gradient(135deg, #ee8131, #ec1613);
							border-radius: 6rpx;
							transform: skewX(-10deg);
							margin-right: -10rpx;
							padding: 5rpx 30rpx 5rpx 15rpx;

							.mem-name-text {
								display: inline-block;
								color: #fff;
								font-size: $font-my-size-24;
								transform: skewX(10deg);
							}
						}
					}

					.persCenter-menu-ls-s-fu-box {
						background: #dbf1eb;
						border-radius: 50rpx;
						font-size: $font-my-size-30;
						font-weight: bold;
						color: #45aa7e;
						padding: 5rpx 40rpx;
					}
				}
			}
		}

		.persCenter-menu-two {
			padding: 30rpx 0rpx;

			.persCenter-menu-two-ls {
				flex: 1;

				.weekMoneyNum {
					font-size: $font-my-size-52;
					font-weight: bold;
				}

				.weekMoneyName {
					font-size: $font-my-size-24;
					color: $font-my-color-6;
				}
			}
		}
	}

	.persCenter-menu-three {
		padding: 30rpx 32rpx 40rpx;

		.persCenter-menu-three-ls {
			padding: 30rpx 0rpx;
			font-size: $font-my-size-34;

			.ls-title {
				color: #666;
			}

			.ls-label {
				color: #999;
			}
		}
	}
}
</style>
