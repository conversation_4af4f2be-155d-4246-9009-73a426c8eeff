<template>
  <view class="content">
    <uni-status-bar></uni-status-bar>
    
    <view class="header">
      <view class="back-btn" @click="goBack">
        <uni-icons type="arrowleft" size="40"></uni-icons>
      </view>
      <view class="title">商品规格</view>
      <view class="save-btn" @click="saveSpec">保存</view>
    </view>
    
    <view class="spec-container">
      <view class="tips">
        <text>添加商品规格后，商品价格将根据规格价格计算</text>
      </view>
      
      <!-- 规格列表 -->
      <view class="spec-list">
        <draggable v-model="specList" animation="300" handle=".drag-handle">
          <view class="spec-item" v-for="(spec, specIndex) in specList" :key="specIndex">
            <view class="spec-header">
              <view class="drag-handle">
                <u-icon name="list-dot" size="24" color="#999"></u-icon>
              </view>
              <input 
                type="text" 
                v-model="spec.name" 
                placeholder="请输入规格名称，如：辣度、温度等" 
                class="spec-name-input"
              />
              <u-icon 
                v-if="specList.length > 1" 
                size="24" 
                color="#ec1616" 
                @click="removeSpec(specIndex)"
              ></u-icon>
            </view>
            
            <view class="spec-options">
              <draggable v-model="spec.options" animation="300" handle=".option-drag-handle">
                <view class="option-item" v-for="(option, optionIndex) in spec.options" :key="optionIndex">
                  <view class="option-drag-handle">
                    <u-icon name="list-dot" size="24" color="#999"></u-icon>
                  </view>
                  <input 
                    type="text" 
                    v-model="option.name" 
                    placeholder="规格值,如：中辣" 
                    class="option-input"
                  />
                  <input 
                    type="digit" 
                    v-model="option.price" 
                    placeholder="加价金额" 
                    class="price-input"
                  />
                  <u-icon 
                    v-if="spec.options.length > 1" 
                    name="close" 
                    size="20" 
                    color="#999" 
                    @click="removeOption(specIndex, optionIndex)"
                  ></u-icon>
                </view>
              </draggable>
              
              <view class="add-option" @click="addOption(specIndex)">
                <u-icon name="plus" size="16" color="#007aff"></u-icon>
                <text>添加规格值</text>
              </view>
            </view>
          </view>
        </draggable>
        
        <view class="add-spec" @click="addSpec">
          <u-icon name="plus" size="16" color="#007aff"></u-icon>
          <text>添加规格</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import draggable from 'vuedraggable'

export default {
  components: {
    draggable
  },
  data() {
    return {
      goodsId: '',
      specList: [
        {
          name: '',
          options: [
            { name: '', price: '' }
          ]
        }
      ]
    }
  },
  
  onLoad(options) {
	  console.log(options)
    if (options.id) {
      this.goodsId = options.id;
    }
	if (options.specData) {
		this.specList = JSON.parse(options.specData).map(spec => ({
            name: spec.specificationName,
            options: spec.specificationOptions.map(option => ({
              name: option.name,
              price: option.price
            }))
        }));
	}
    // 加载已有的规格数据
    this.loadSpecData();
  },
  
  methods: {
    // 加载商品规格数据
    async loadSpecData() {
      if (!this.goodsId) {
        // 新增商品时不需要加载数据
        return;
      }
      
      try {
        uni.showLoading({ title: '加载中...' });
        
        const res = await this.$request({
          url: '/api-goods/rest/merchant/goods/getById',
          method: 'POST',
          data: { id: this.goodsId }
        });
        
        if (res.code === 200 && res.data.goodsSpecification) {
          // 转换数据格式
          this.specList = res.data.goodsSpecification.map(spec => ({
            name: spec.specificationName,
            options: spec.specificationOptions.map(option => ({
              name: option.name,
              price: option.price
            }))
          }));
          
          // 如果没有规格数据，则初始化一个空的规格
          if (this.specList.length === 0) {
            this.specList = [{ name: '', options: [{ name: '', price: '' }] }];
          }
        }
      } catch (error) {
        console.error('加载规格数据失败:', error);
        uni.showToast({ title: '加载失败', icon: 'none' });
      } finally {
        uni.hideLoading();
      }
    },
    
    // 添加规格
    addSpec() {
      this.specList.push({
        name: '',
        options: [
          { name: '', price: '' }
        ]
      });
    },
    
    // 删除规格
    removeSpec(index) {
      if (this.specList.length <= 1) {
        uni.showToast({ title: '至少保留一个规格', icon: 'none' });
        return;
      }
      this.specList.splice(index, 1);
    },
    
    // 添加规格选项
    addOption(specIndex) {
      this.specList[specIndex].options.push({ name: '', price: '' });
    },
    
    // 删除规格选项
    removeOption(specIndex, optionIndex) {
      if (this.specList[specIndex].options.length <= 1) {
        uni.showToast({ title: '至少保留一个选项', icon: 'none' });
        return;
      }
      this.specList[specIndex].options.splice(optionIndex, 1);
    },
    
    // 验证规格数据
    validateSpec() {
      for (let i = 0; i < this.specList.length; i++) {
        const spec = this.specList[i];
        if (!spec.name.trim()) {
          uni.showToast({ title: `第${i + 1}个规格名称不能为空`, icon: 'none' });
          return false;
        }
        
        for (let j = 0; j < spec.options.length; j++) {
          const option = spec.options[j];
          if (!option.name.trim()) {
            uni.showToast({ title: `规格"${spec.name}"的第${j + 1}个选项名称不能为空`, icon: 'none' });
            return false;
          }
          
          if (option.price && !/^\d+(\.\d{1,2})?$/.test(option.price)) {
            uni.showToast({ title: `规格"${spec.name}"的选项"${option.name}"价格格式不正确`, icon: 'none' });
            return false;
          }
        }
      }
      return true;
    },
    
    // 保存规格
    saveSpec() {
      if (!this.validateSpec()) {
        return;
      }
      
      // 转换数据格式
      const specData = this.specList.map((spec, index) => ({
        specificationName: spec.name,
        sortNumber: index + 1,
        specificationOptions: spec.options.map((option, optionIndex) => ({
          name: option.name,
          price: option.price ? parseFloat(option.price) : 0,
          sortNumber: optionIndex + 1
        }))
      }));
	  console.log('============', specData)
      
      // 保存到本地存储，供商品编辑页面使用
      uni.setStorageSync('goodsSpecificationList', specData);
      
      uni.showToast({
        title: '保存成功',
        icon: 'success'
      });
      
      setTimeout(() => {
        uni.navigateBack();
      }, 1000);
    },
    
    // 返回上一页
    goBack() {
      uni.navigateBack();
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  position: relative;
  width: 750rpx;
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow-x: hidden;
  background-color: #f5f5f5;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10rpx 10rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
  
  .back-btn, .save-btn {
    width: 80rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .title {
    flex: 1;
    font-size: 32rpx;
    font-weight: bold;
    text-align: center;
  }
  
  .save-btn {
    color: #007aff;
    font-size: 28rpx;
  }
}

.spec-container {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
}

.tips {
  background-color: #e8f4ff;
  padding: 20rpx;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
  
  text {
    font-size: 24rpx;
    color: #666;
  }
}

.spec-list {
  .spec-item {
    background-color: #fff;
    border-radius: 12rpx;
    padding: 20rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    
    .spec-header {
      display: flex;
      align-items: center;
      margin-bottom: 20rpx;
      padding-bottom: 20rpx;
      border-bottom: 1rpx solid #f0f0f0;
      
      .spec-name-input {
        flex: 1;
        height: 60rpx;
        border: 1rpx solid #e0e0e0;
        border-radius: 8rpx;
        margin: 0 10rpx;
        padding: 0 20rpx;
        font-size: 28rpx;
      }
      
      .drag-handle {
        margin: 0 10rpx;
        cursor: move;
      }
    }
    
    .spec-options {
      .option-item {
        display: flex;
        align-items: center;
        margin-bottom: 16rpx;
        
        .option-input, .price-input {
          height: 60rpx;
          border: 1rpx solid #e0e0e0;
          border-radius: 8rpx;
          padding: 0 20rpx;
          font-size: 26rpx;
        }
        
        .option-input {
          flex: 2;
          margin-right: 16rpx;
        }
        
        .price-input {
          flex: 1;
        }
        
        .option-drag-handle {
          margin: 0 10rpx;
          cursor: move;
        }
        
        .u-icon {
          margin-left: 10rpx;
        }
      }
      
      .add-option {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 60rpx;
        border: 1rpx dashed #007aff;
        border-radius: 8rpx;
        color: #007aff;
        font-size: 26rpx;
        margin-top: 10rpx;
        
        text {
          margin-left: 10rpx;
        }
      }
    }
  }
  
  .add-spec {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 80rpx;
    background-color: #fff;
    border-radius: 12rpx;
    color: #007aff;
    font-size: 28rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    
    text {
      margin-left: 10rpx;
    }
  }
}
</style>