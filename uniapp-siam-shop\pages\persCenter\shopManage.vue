<template>
  <view class="container">
    <view class="order-main">
      <u-form
        :model="formData"
        ref="uForm"
        :error-type="['message']"
        :label-width="150"
      >
        <view class="orderSet orderSet-l">
          <u-form-item label="营业状态" prop="isOperating">
            <view
              :class="{ checked: formData.isOperating === 0, 'check-box': true }"
              @click="formData.isOperating = 0"
            >
              <text class="value-name">打烊</text>
            </view>
            <view
              :class="{ checked: formData.isOperating === 1, 'check-box': true }"
              @click="formData.isOperating = 1"
            >
              <text class="value-name">营业</text>
            </view>
            <view
              :class="{ checked: formData.isOperating === 2, 'check-box': true }"
              @click="formData.isOperating = 2"
            >
              <text class="value-name">停业</text>
            </view>
          </u-form-item>
          <u-form-item label="接单方式" prop="isAutomatic">
            <view
              :class="{ checked: formData.isAutomatic === 0, 'check-box': true }"
              @click="formData.isAutomatic = 0"
            >
              <text class="value-name">自动接单</text>
            </view>
            <view
              :class="{ checked: formData.isAutomatic === 1, 'check-box': true }"
              @click="formData.isAutomatic = 1"
            >
              <text class="value-name">手动接单</text>
            </view>
          </u-form-item>
          <u-form-item label="延迟时间" prop="automaticTime" v-if="formData.isAutomatic === 0">
            <u-number-box :min="1" :max="4" :input-width="100" :input-height="60"></u-number-box>
            <view style="font-size: 24rpx;margin-left: 10rpx;">自动接单延迟时间（分钟）</view>
          </u-form-item>
           <u-form-item label="预约单" prop="isReservation">
            <view
              :class="{ checked: formData.isReservation === 1, 'check-box': true }"
              @click="formData.isReservation = 1"
            >
              <text class="value-name">开启</text>
            </view>
            <view
              :class="{ checked: formData.isReservation === 0, 'check-box': true }"
              @click="formData.isReservation = 0"
            >
              <text class="value-name">关闭</text>
            </view>
          </u-form-item>
          <u-form-item label="配送费" prop="deliveryFee">
            <uni-easyinput
              class="name-cont"
              type="text"
              v-model="formData.deliveryFee"
              :inputBorder="false"
              placeholder="金额(元)"
            ></uni-easyinput>
          </u-form-item>
          <u-form-item label="包装费" prop="packagingFee">
            <uni-easyinput
              class="name-cont"
              type="text"
              v-model="formData.packagingFee"
              :inputBorder="false"
              placeholder="金额(元)"
            ></uni-easyinput>
          </u-form-item>
          <u-form-item label="起送价格" prop="startDeliveryPrice">
            <uni-easyinput
              class="name-cont"
              type="text"
              v-model="formData.startDeliveryPrice"
              :inputBorder="false"
              placeholder="金额(元)"
            ></uni-easyinput>
          </u-form-item>
          <u-form-item label="开始时间" prop="startTime">
            <view
              class="flex flex-align-start"
              @click="showStartTimePicker = true"
            >
              <text class="value-name" v-if="formData.startTime">{{
                formData.startTime
              }}</text>
              <text class="value-name printer-placeholder" v-else
                >请选择开始时间</text
              >
              <uni-icons
                class="iconStyl"
                color="#999"
                type="forward"
              ></uni-icons>
            </view>
          </u-form-item>
          <u-form-item label="结束时间" prop="endTime">
            <view
              class="flex flex-align-start"
              @click="showEndTimePicker = true"
            >
              <text class="value-name" v-if="formData.endTime">{{
                formData.endTime
              }}</text>
              <text class="value-name printer-placeholder" v-else
                >请选择结束时间</text
              >
              <uni-icons
                class="iconStyl"
                color="#999"
                type="forward"
              ></uni-icons>
            </view>
          </u-form-item>
          <u-form-item label="配送范围" prop="deliveryRange">
            <view class="delivery-range">
              <view
                :class="{
                  checked: formData.deliveryRange.includes('1'),
                  'check-box': true,
                }"
                @click="toggleDeliveryRange('1')"
              >
                <text class="value-name">北苑</text>
              </view>
              <view
                :class="{
                  checked: formData.deliveryRange.includes('2'),
                  'check-box': true,
                }"
                @click="toggleDeliveryRange('2')"
              >
                <text class="value-name">滨江</text>
              </view>
              <view
                :class="{
                  checked: formData.deliveryRange.includes('3'),
                  'check-box': true,
                }"
                @click="toggleDeliveryRange('3')"
              >
                <text class="value-name">新校区</text>
              </view>
            </view>
          </u-form-item>
          <u-form-item label="配送方式" prop="deliveryWay">
            <view
              :class="{
                checked: formData.deliveryWay == 1,
                'check-box': true,
              }"
              @click="formData.deliveryWay = 1"
            >
              <text class="value-name">自配送</text>
            </view>
            <view
              :class="{
                checked: formData.deliveryWay == 2,
                'check-box': true,
              }"
              @click="formData.deliveryWay = 2"
            >
              <text class="value-name">平台配送</text>
            </view>
            <view
              :class="{
                checked: formData.deliveryWay == 3,
                'check-box': true,
              }"
              @click="formData.deliveryWay = 3"
            >
              <text class="value-name">平台+自配送</text>
            </view>
          </u-form-item>
          <u-form-item label="绑定打印机" prop="selectedPrinter">
            <view class="flex flex-align-start" @click="openPrinterSelector">
              <text
                class="value-name printer-value"
                v-if="formData.selectedPrinter && formData.selectedPrinter.name"
                >{{ formData.selectedPrinter.name }}</text
              >
              <text class="value-name printer-placeholder" v-else
                >请选择打印机</text
              >
              <uni-icons
                class="iconStyl"
                color="#999"
                type="forward"
              ></uni-icons>
            </view>
          </u-form-item>

          <u-form-item label="门店头像（宽400px * 高400px）">
            <u-upload
              :max-count="1"
              ref="shopLogoImgFile"
              :action="action"
              :header="header"
              @on-success="onSuccess"
              :file-list="shopLogoImgList"
            ></u-upload>
          </u-form-item>

          <u-form-item label="门店封面（宽600px * 高355px）">
            <u-upload
              :max-count="1"
              ref="shopLogoImgFile"
              :action="action"
              :header="header"
              @on-success="onSuccess"
              :file-list="firstPosterImgList"
            ></u-upload>
          </u-form-item>

          <u-form-item
            label="店内照片（宽600px * 高355px）"
            prop="shopWithinImgFile"
          >
            <u-upload
              :max-count="9"
              ref="shopWithinImgFile"
              :action="action"
              :header="header"
              @on-success="onSuccess"
              :file-list="shopWithinImgList"
            ></u-upload>
          </u-form-item>
          <!-- <view class="orderSet-ls">
						<u-form-item label="听单设置" prop="announcement">
							<view class="flex flex-align-start">
								<text class="value-name">运费大于15元</text>
								<uni-icons class="iconStyl" color="#999" type="forward"></uni-icons>
							</view>
							<view class="trip-name">只有配送距离大于5公里的订单才会以"听单"形式提醒</view>
						</u-form-item>
						<my-line></my-line> -->
        </view>
      </u-form>
      <my-button
        margin-top="60"
        :bold="true"
        color="#fff"
        font-size="32"
        @confirm="UpLine"
        >保存</my-button
      >
    </view>
    <!-- 开始时间选择器 -->
    <u-picker
      mode="time"
      v-model="showStartTimePicker"
      :params="timeParams"
      @confirm="onStartTimeConfirm"
    ></u-picker>

    <!-- 结束时间选择器 -->
    <u-picker
      mode="time"
      v-model="showEndTimePicker"
      :params="timeParams"
      @confirm="onEndTimeConfirm"
    ></u-picker>

    <!-- 打印机选择弹窗 -->
    <printer-selector
      :value="showPrinterSelector"
      :current-printer="formData.selectedPrinter"
      @confirm="onPrinterSelected"
      @cancel="this.showPrinterSelector = false"
    ></printer-selector>
  </view>
</template>

<script>
import { mapState } from "vuex";
import PrinterSelector from "../index/components/printerSelector.vue";
import { getImageUrl } from '@/utils';

export default {
  props: {},
  components: {
    PrinterSelector, // 注册打印机选择组件
  },
  computed: {
    ...mapState(["UserInfo", "configInfo"]),
  },
  data() {
    return {
      show: false,
	  action: '',
      shopLogoImgList: [],
      shopWithinImgList: [],
      firstPosterImgList: [],
	  header: {},
      formData: {
        deliveryWay: 1, // 配送方式 1=商家自配送 2=平台配送 3=商家+平台配送
        deliveryFee: 0, // 配送费
        isAutomatic: 1, // 接单方式（0自动接单 1手动接单）
        isReservation: 1, //  是否接受预约单（0不接受 1接受）
        packagingFee: 0,
        startDeliveryPrice: 0,
        isOperating: 0, //0打烊 1营业 2停业
        startTime: "", // 开始时间
        endTime: "", // 结束时间
        deliveryRange: [], // 配送范围
        // 打印机相关数据
        selectedPrinter: {},
        announcement: "",
      },
      // 时间选择器相关
      showStartTimePicker: false,
      showEndTimePicker: false,
      showPrinterSelector: false,
      timeParams: {
        hour: true,
        minute: true,
        second: false,
      },
    };
  },
  mounted() {
	uni.$emit("RefreshUserInfo");
    const account = uni.getStorageSync("account")
    this.action = account.domainUrl + '/api-util/rest/merchant/uploadSingleImage';
    this.header = {
      'Authorization': account.authorization,
      // 'uniacid': account ? account.uniacid : "43",
      'module': "yb_ps",
      'userId': account ? account.userId : "",
      'appType': 'weChat'
    }
    this.init();
  },
  methods: {
    init() {
      this.show = true;
      this.formData.deliveryFee = this.UserInfo.deliveryFee;
      this.formData.packagingFee = this.UserInfo.packagingFee;
      this.formData.startDeliveryPrice = this.UserInfo.startDeliveryPrice;
      this.formData.deliveryWay = this.UserInfo.deliveryWay;
      this.formData.isOperating = this.UserInfo.isOperating;
      this.formData.isReservation = this.UserInfo.isReservation;
      this.formData.isAutomatic = this.UserInfo.isAutomatic;
      this.formData.startTime = this.UserInfo.startTime || "";
      this.formData.endTime = this.UserInfo.endTime || "";
      this.formData.announcement = this.UserInfo.announcement;
      this.formData.shopLogoImg = this.UserInfo.shopLogoImg;
      this.formData.shopWithinImg = this.UserInfo.shopWithinImg;
      this.formData.firstPoster = this.UserInfo.firstPoster;
      // 初始化配送范围
      if (this.UserInfo.deliveryRange) {
        this.formData.deliveryRange = this.UserInfo.deliveryRange
          .split(",")
          .filter((item) => item);
      } else {
        this.formData.deliveryRange = [];
      }
      if (this.formData.shopLogoImg) {
        this.shopLogoImgList = this.formData.shopLogoImg.split(',').map(url => ({
          url: getImageUrl(url),
          progress: 100
        }));
      }
      if (this.formData.firstPoster) {
        this.firstPosterImgList = this.formData.firstPoster.split(',').map(url => ({
          url: getImageUrl(url),
          progress: 100
        }));
      }
	  console.log('this.firstPosterImgList', this.firstPosterImgList);
      
      if (this.formData.shopWithinImg) {
        this.shopWithinImgList = this.formData.shopWithinImg.split(',').map(url => ({
          url: getImageUrl(url),
          progress: 100
        }));
      }

      // 初始化打印机信息
      this.formData.selectedPrinter = {
        id: this.UserInfo.checkoutPrinterId || null,
        name: this.UserInfo.checkoutPrinterName || "",
      };
    },
    onSuccess (data) {

    },

    // 开始时间确认
    onStartTimeConfirm(e) {
      this.formData.startTime = e.hour + ":" + e.minute;
    },

    // 结束时间确认
    onEndTimeConfirm(e) {
      this.formData.endTime = e.hour + ":" + e.minute;
    },

    // 切换配送范围选项
    toggleDeliveryRange(value) {
      const index = this.formData.deliveryRange.indexOf(value);
      if (index > -1) {
        this.formData.deliveryRange.splice(index, 1);
      } else {
        this.formData.deliveryRange.push(value);
      }
    },

    // 打开打印机选择器
    openPrinterSelector() {
      this.showPrinterSelector = true;
    },

    // 打印机选择确认回调
    onPrinterSelected(printerInfo) {
      // 查找选中的打印机名称
      this.formData.selectedPrinter = printerInfo || {};
      this.showPrinterSelector = false;
    },
    getImgUrl (refName) {
      const list = this.$refs[refName].lists.filter(val => {
        return val.progress == 100;
      })
      return list.map(item => (item.response?.data || item.url).replace('https://shop-beingwell.oss-cn-hangzhou.aliyuncs.com/', ''))?.join();
    },

    UpLine() {
      // 店铺营业/打烊
      if (parseInt(this.UserInfo.auditStatus) == 2) {
		const shopLogoImgFile = this.getImgUrl('shopLogoImgFile');

		const shopWithinImgFile = this.getImgUrl('shopWithinImgFile');
        let id = this.UserInfo.shopId;
        this.$request({
          url: "/api-merchant/rest/merchant/shop/update",
          data: {
            ...this.formData,
            id: id,
            isOperating: this.formData.isOperating,
            deliveryFee: this.formData.deliveryFee,
            packagingFee: this.formData.packagingFee,
            startDeliveryPrice: this.formData.startDeliveryPrice,
            deliveryWay: this.formData.deliveryWay,
            startTime: this.formData.startTime,
            endTime: this.formData.endTime,
            deliveryRange: this.formData.deliveryRange.join(","),
            checkoutPrinterId: this.formData.selectedPrinter.id, // 添加打印机ID
			shopLogoImg: shopLogoImgFile,
			shopWithinImg: shopWithinImgFile,
          },
        }).then((res) => {
          // #ifdef MP-WEIXIN
          res = JSON.parse(res);
          // #endif
          if (res.code === 200) {
            uni.$emit("RefreshUserInfo");
          } else {
            this.$interactive.ShowToast({ title: res.message }, false);
          }
          this.$interactive.ShowToast({ title: "保存成功！" }).then(() => {
            uni.navigateBack();
          });
        });
      } else {
        this.$interactive.ShowToast({ title: "请先进行实名认证！" }, false);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  background: $uni-bg-color-grey;
  padding: 20rpx;
  height: 100%;
}
.order-main {
  background: #fff;
  padding: 20rpx;
  border-radius: 40rpx;

  .order-title {
    padding: 30rpx 0;
    background: #ffffff;
    text-align: center;
    font-size: $font-my-size-34;
  }

  .title-name {
    font-size: $font-my-size-32;
    font-weight: bold;
    color: $font-my-color-3;
    flex: 1;
  }
  .check-box {
    border: 1rpx solid $font-my-color-9;
    border-radius: 10rpx;
    padding: 10rpx;
    font-size: 28rpx;
    line-height: 32rpx;
    margin-left: 20rpx;
  }
  .checked {
    border: 1rpx solid #fa7c25;
    .value-name {
      color: #fa7c25;
    }
  }

  .value-name {
    color: $font-my-color-3;
  }

  .printer-value {
    color: $font-my-color-3;
  }

  .printer-placeholder {
    color: $font-my-color-9;
  }

  .trip-name {
    color: $font-my-color-9;
    font-size: $font-my-size-24;
    margin-top: 20rpx;
  }

  .iconStyl {
    margin-top: 4rpx;
  }

  .delivery-range {
    display: flex;
  }
}
</style>
