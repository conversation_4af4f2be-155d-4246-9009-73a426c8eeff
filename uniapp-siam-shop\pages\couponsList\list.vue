<template>
  <view class="content">
    <uni-status-bar></uni-status-bar>
    <view class="content-box">
      <mescroll-uni ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback" :down="{ auto: true }" :top="-60"
      :up="{ auto: false, empty: {tip: '暂无优惠券数据' } }">
      <view class="contInfo">
        <template v-for="(item, index) in couponsList" >
          <view class="coupon-item margin-bottom">
            <view class="contInfo-title-box">
              <view class="contInfo-title">
                <view class="contTitle">名称:{{ item.name }}</view>
                <view class="contTitle-label">
                  <!-- <template v-if="item.preferentialType === 1">
                    满 ¥{{ item.limitedPrice }} 减 ¥{{ item.reducedPrice }}
                  </template> -->
                  <template >
                    折扣 {{ item.discountAmount }}折
                  </template>
                </view>

              </view>
            </view>
            <view class="description" v-if="item.description">
              <text>规则:{{ item.description }}</text>
            </view>
            <view class="validity">
              <text v-if="item.validType === 1">有效期: {{ item.validStartTime }} 至 {{ item.validEndTime }}</text>
              <text v-else-if="item.validType === 2">领取后 {{ item.validDays }} 天内有效</text>
            </view>
            <view class="coupon-stats">
              <text class="stat-item">已发放张数: {{ item.gaveCount || 0 }}</text>
              <text class="stat-item">已使用张数: {{ item.usedCount || 0 }}</text>
            </view>
            <view class="footerBoten">
              <view class="action-btn edit-btn" @click.stop="onMass(item)">群发优惠劵</view>
              <view class="action-btn edit-btn" @click.stop="editCoupon(item)">编辑</view>
              <view class="action-btn link-btn" @click.stop="linkGoods(item)">关联商品</view>
              <view class="action-btn delete-btn" @click.stop="deleteCoupon(item)">删除</view>
            </view>
          </view>
        </template>
      </view>
    </mescroll-uni>
    </view>
    
    <!-- 新增优惠券按钮 -->
    <view class="add-btn" @click="addCoupon">
      <text class="add-icon">+</text>
    </view>
    
  </view>
</template>

<script>
import CustomTabsSwiper from '../../components/custom-tabs-page-linkge/tab-page-linkage.vue'; // 全屏联动
export default {
  components: {
    CustomTabsSwiper
  },
  data () {
    return {
      couponsList: []
    }
  },
  onShow() {
    // 检查是否需要刷新列表
    const needRefresh = uni.getStorageSync('couponsListNeedRefresh');
    if (needRefresh) {
      // 清除标记
      uni.removeStorageSync('couponsListNeedRefresh');
      // 刷新列表
      this.refresh();
    }
  },
  methods: {
    // 初始化mescroll对象
    mescrollInit(mescroll) {
      this.mescroll = mescroll;
    },
    // 主动触发下拉刷新
    refresh () {
      if (this.mescroll) {
        this.mescroll.triggerDownScroll();
      }
    },

    // 下拉回调函数
    downCallback (e) {
      this.mescroll.resetUpScroll();
    },
    // 上拉加载更多
    upCallback (e) {
      this.getCouponsList(e.num);
    },

    // 新增优惠券
    addCoupon() {
      uni.navigateTo({
        url: '/pages/couponsList/edit'
      });
    },
    
    // 关联商品
    linkGoods(item) {
      uni.navigateTo({
        url: `/pages/couponsList/linkGoods?id=${item.id}&name=${encodeURIComponent(item.name)}`
      });
    },
    // 编辑优惠券
    editCoupon(item) {
      const formData = {
          name: item.name || '',
          preferentialType: item.preferentialType || 1,
          discountAmount: item.discountAmount || '',
          limitedPrice: item.limitedPrice || '',
          reducedPrice: item.reducedPrice || '',
          description: item.description || '',
          validType: item.validType || 1,
          validStartTime: item.validStartTime || '',
          validEndTime: item.validEndTime || '',
          validDays: item.validDays || '',
          id: item.id
        }
      uni.navigateTo({
        url: '/pages/couponsList/edit?data=' + JSON.stringify(item)
      });
    },
    // 群发优惠券
    onMass(item) {
      uni.showModal({
        title: '提示',
        content: `确定要群发该优惠券吗？`,
        success: async(res) => {
          if (res.confirm) {
            const res = await this.$request({
              url: '/api-promotion/rest/merchant/couponsMemberRelation/giveCoupons',
              method: 'post',
              data: { couponsId: item.id }
            });
            if (res.code === 200) {
              uni.showToast({
                title: '群发成功',
                icon: 'success'
              });
              this.refresh();
            } else {
              uni.showToast({
                title: res.message || '群发失败',
                icon: 'none'
              });
            }
          }
        }
      });
    },
    // 删除优惠券
    deleteCoupon(item) {
      uni.showModal({
        title: '提示',
        content: `确定要删除该优惠券吗？`,
        success: async(res) => {
          if (res.confirm) {
            const res = await this.$request({
              url: '/api-promotion/rest/merchant/coupons/delete',
              method: 'delete',
              data: { id: item.id }
            });
            if (res.code === 200) {
              uni.showToast({
                title: '删除成功',
                icon: 'success'
              });
              this.refresh();
            } else {
              uni.showToast({
                title: res.message || '删除失败',
                icon: 'none'
              });
            }
          }
        }
      });
    },
    // 获取优惠券列表
    async getCouponsList (page = 1) {
      try {
        let url = '/api-promotion/rest/merchant/coupons/list';
        let params = {
          pageNo: page,
          pageSize: 10,
          isDelete: 0,
          source: 1
        };
      
        
        this.$request({
          url,
          data: params
        }).then(res => {
          if (res.code === 200) {
            if (page === 1) {
              this.couponsList = res.data.records;
            } else {
              this.couponsList = this.couponsList.concat(res.data.records);
            }
            this.mescroll.endSuccess(res.data.records.length, res.data.records.length >= 10);
          } else {
            this.mescroll.endSuccess(0, false);
          }
        }).catch(err => {
          this.mescroll.endErr();
        });
      } catch (e) {
        console.error('error:', e);
        this.mescroll.endErr();
      }
    }
  }
}
</script>

<style lang="scss" scoped>
$borderColor: rgba(0, 0, 0, 0.8);
.content {
  position: relative;
  width: 750rpx;
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow-x: hidden;
  background-color: $uni-bg-color-grey;
  .content-box {
    position: relative;
    flex: 1;
    width: 100%;
    height: 100%;
  }
}

.contInfo {
  padding: 20rpx;
}

.coupon-item {
  position: relative;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.contInfo-title-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.contInfo-title {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.contTitle {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.contTitle-label {
  font-size: 28rpx;
  color: #fa7c25;
  margin-bottom: 8rpx;
}

.description {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.validity {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 12rpx;
}

.coupon-stats {
  display: flex;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.stat-item {
  margin-right: 20rpx;
}


.footerBoten {
  display: flex;
  justify-content: flex-end;
  margin-top: 16rpx;
  border-top: 1rpx solid #f0f0f0;
  padding-top: 16rpx;
}

.action-btn {
  padding: 8rpx 24rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  margin-left: 16rpx;
}

.edit-btn {
  background-color: #e6f7ff;
  color: #1890ff;
}

.link-btn {
  background-color: #f6ffed;
  color: #52c41a;
}

.delete-btn {
  background-color: #fff1f0;
  color: #ff4d4f;
}

.add-btn {
  position: fixed;
  right: 30rpx;
  bottom: 100rpx;
  width: 100rpx;
  height: 100rpx;
  background-color: #fa7c25;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(250, 124, 37, 0.4);
  z-index: 999;
}

.add-icon {
  color: #fff;
  font-size: 60rpx;
  font-weight: bold;
  line-height: 1;
}
</style>