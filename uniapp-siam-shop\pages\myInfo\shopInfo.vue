<template>
  <view class="perfectInfo">
    <u-select v-model="deliveryWayShow" mode="single-column" :list="deliveryWayList" @confirm="onDeliveryWayConfirm"></u-select>
    <!-- 添加类别选择弹窗 -->
    <category-multi-select 
      :show="categorySelectShow" 
      :value="selectedCategoryIds" 
      :category-list="categoryList"
      @confirm="onCategoryConfirm"
      @close="categorySelectShow = false"
    ></category-multi-select>
    
    <u-form :model="formData" ref="uForm" :error-type="['message']" :label-width="150">

      <!-- 门店名称 -->
      <u-form-item label="门店名称" prop="name" :required="true">
        <uni-easyinput class="input" :inputBorder="false" v-model="formData.name" placeholder="请输入门店名称"></uni-easyinput>
      </u-form-item>

      <!-- <u-form-item label="配送费" prop="deliveryFee" :required="true">
        <uni-easyinput class="input" :inputBorder="false" v-model="formData.deliveryFee"
          placeholder="请输入配送费"></uni-easyinput>
      </u-form-item> -->

      <!-- <u-form-item label="配送方式" prop="deliveryWay" :required="true">
          <view class="input flex flex-align-center justify-space-between" style="width: 100%;" @click="showDeliveryWaySelect">
            <text class="flex1">{{ getdeliveryWayText() || '请选择'}}</text>
            <uni-icons type="forward"></uni-icons>
          </view>
      </u-form-item> -->

      <u-form-item label="联系人姓名" prop="contactRealname" :required="true">
        <uni-easyinput class="input" :inputBorder="false" v-model="formData.contactRealname"
          placeholder="请输入联系人姓名"></uni-easyinput>
      </u-form-item>

      <u-form-item label="联系电话" prop="contactPhone" :required="true">
        <uni-easyinput class="input" :inputBorder="false" v-model="formData.contactPhone"
          placeholder="请输入联系电话"></uni-easyinput>
      </u-form-item>

      <u-form-item label="门店地址" prop="street" :required="true">
        <view class="input flex flex-align-center justify-space-between" style="width: 100%;" @click="selectLocation">
          <text class="flex1">{{ formData.street || '点击选择门店地址'}}</text>
          <uni-icons type="forward"></uni-icons>
        </view>
      </u-form-item>

      <u-form-item label="门牌号" prop="houseNumber" :required="true">
        <uni-easyinput class="input" :inputBorder="false" v-model="formData.houseNumber"
          placeholder="请输入门牌号"></uni-easyinput>
      </u-form-item>

      <!-- <u-form-item label="门店公告" prop="announcement" :required="true">
        <uni-easyinput class="input" :inputBorder="false" v-model="formData.announcement"
          placeholder="请输入门店公告"></uni-easyinput>
      </u-form-item> -->

      <!-- 修改营业类目为选择模式 -->
      <u-form-item label="营业类目" prop="managePrimary" :required="true">
        <view class="input flex flex-align-center justify-space-between" style="width: 100%;" @click="showCategorySelect">
          <text class="flex1">{{ getSelectedCategoryNames() || '请选择营业类目'}}</text>
          <uni-icons type="forward"></uni-icons>
        </view>
      </u-form-item>

      <!-- <u-form-item label="门店头像（宽400px * 高400px）" :required="true">
        <u-upload :max-count="9" ref="shopLogoImgFile" :action="action" :header="header"
          @on-success="onSuccess" :file-list="shopLogoImgList"></u-upload>
      </u-form-item> -->

      <!-- <u-form-item label="店内照片（宽600px * 高355px）" prop="shopWithinImgFile" :required="true">
        <u-upload :max-count="9" ref="shopWithinImgFile" :action="action" :header="header"
          @on-success="onSuccess" :file-list="shopWithinImgList"></u-upload>
      </u-form-item> -->

      <u-form-item label="营业执照" prop="businessLicenseFile" :required="true">
        <u-upload :max-count="1" ref="businessLicenseFile" :action="action" :header="header"
          @on-success="onSuccess" :file-list="businessLicenseImgList"></u-upload>
      </u-form-item>

      <u-form-item label="身份证正面" prop="idCardFrontSideFile" :required="true">
        <u-upload :max-count="1" ref="idCardFrontSideFile" :action="action" :header="header"
          @on-success="onSuccess" :file-list="idCardFrontSideImgList"></u-upload>
      </u-form-item>

      <u-form-item label="身份证反面" prop="idCardBackSideFile" :required="true">
        <u-upload :max-count="1" ref="idCardBackSideFile" :action="action" :header="header"
          @on-success="onSuccess" :file-list="idCardBackSideImgList"></u-upload>
      </u-form-item>

    </u-form>

    <my-button margin-top="60" :bold="true" color="#fff" font-size="32" @confirm="submit">保存</my-button>
  </view>
</template>

<script>
import CategoryMultiSelect from '@/pages/goods/components/category-multi-select.vue';
import { getImageUrl } from '@/utils';

export default {
  components: {
    CategoryMultiSelect
  },
  data () {
    return {
      isEdit: false,
      action: '',
      header: {},
      fileList: [],
      deliveryWayShow: false,
      // 添加类别选择相关数据
      categorySelectShow: false,
      categoryList: [],
      selectedCategoryIds: [],
      selectedCategoryNames: [],
      // 添加图片列表数据
      shopLogoImgList: [],
      shopWithinImgList: [],
      businessLicenseImgList: [],
      idCardFrontSideImgList: [],
      idCardBackSideImgList: [],
      formData: {
        name: '',
        fileList: [],
        deliveryWay: null
      },
      // 表单验证规则
      rules: {
        name: [
          {
            required: true,
            message: '请输入门店名称',
            trigger: ['change', 'blur'],
          }
        ],
        // deliveryWay: [
        //   {
        //     required: true,
        //     message: '请选择配送方式',
        //     trigger: ['change', 'blur'],
        //     validator: (rule, value, callback) => {
        //       return !!value;
        //     }
        //   }
        // ],
        // deliveryFee: [
        //   {
        //     required: true,
        //     message: '请输入配送费',
        //     trigger: ['change', 'blur'],
        //     validator: (rule, value, callback) => {
        //       return value >= 0;
        //     }
        //   }
        // ],
        contactRealname: [
          {
            required: true,
            message: '请输入联系人姓名',
            trigger: ['change', 'blur'],
          }
        ],
        contactPhone: [
          {
            required: true,
            message: '请输入正确的联系电话',
            trigger: ['change', 'blur'],
            validator: (rule, value, callback) => {
              return /^1(3|4|5|7|8|9)\d{9}$/.test(value)
            }
          }
        ],
        street: [
          {
            required: true,
            message: '请输入门店地址',
            trigger: ['change', 'blur'],
          }
        ],
        houseNumber: [
          {
            required: true,
            message: '请输入门牌号',
            trigger: ['change', 'blur'],
          }
        ],
        // announcement: [
        //   {
        //     required: true,
        //     message: '请输入门店公告',
        //     trigger: ['change', 'blur'],
        //   }
        // ],
        managePrimary: [
          {
            required: true,
            message: '请选择营业类目',
            trigger: ['change', 'blur'],
          }
        ],
        // shopLogoImgFile: [
        //   {
        //     required: true,
        //     message: '请选择门店头像',
        //     trigger: ['change', 'blur'],
        //   }
        // ],
      },
      deliveryWayList: [
        { label: '商家自配送', value: 1 },
        { label: '平台配送', value: 2 },
        { label: '商家+平台配送', value: 3 },
      ]
    }
  },
  onReady () {
    this.$refs.uForm.setRules(this.rules);
  },
  async onLoad (options) {
    const account = uni.getStorageSync("account")
    this.action = account.domainUrl + '/api-util/rest/merchant/uploadSingleImage';
    this.header = {
      'Authorization': account.authorization,
      // 'uniacid': account ? account.uniacid : "43",
      'module': "yb_ps",
      'userId': account ? account.userId : "",
      'appType': 'weChat'
    }

    // 获取商品类别列表
    await this.getCategoryList();

    const res = await this.$request({
      url: '/api-merchant/rest/merchant/shop/getLoginMerchantShopInfo',
      method: 'POST',
      data: {}
    });
    this.formData = {
      ...res.data
    }
    
    // 初始化已选择的类别
    if (this.formData.managePrimary) {
      const ids = this.formData.managePrimary.split(',');
      this.selectedCategoryNames = this.categoryList
        .filter(item => ids.includes(item.id.toString()))
        .map(item => item.name);
    }
    
    // 初始化图片列表
    this.initImageLists();
    

    // 监听位置选择事件
    uni.$on('locationSelected', this.onLocationSelected);
  },
  beforeDestroy() {
    // 移除监听事件
    uni.$off('locationSelected', this.onLocationSelected);
  },
  methods: {
    onSuccess (data) {

    },
    // 初始化图片列表
    initImageLists() {
      if (this.formData.shopLogoImg) {
        this.shopLogoImgList = this.formData.shopLogoImg.split(',').map(url => ({
          url: getImageUrl(url),
          progress: 100
        }));
      }
      
      if (this.formData.shopWithinImg) {
        this.shopWithinImgList = this.formData.shopWithinImg.split(',').map(url => ({
          url: getImageUrl(url),
          progress: 100
        }));
      }
      
      if (this.formData.businessLicense) {
        this.businessLicenseImgList = [{
          url: getImageUrl(this.formData.businessLicense),
          progress: 100
        }];
      }
      
      if (this.formData.idCardFrontSide) {
        this.idCardFrontSideImgList = [{
          url: getImageUrl(this.formData.idCardFrontSide),
          progress: 100
        }];
      }
      
      if (this.formData.idCardBackSide) {
        this.idCardBackSideImgList = [{
          url: getImageUrl(this.formData.idCardBackSide),
          progress: 100
        }];
      }
    },
    showDeliveryWaySelect() {
      this.deliveryWayShow = true
    },
    // 显示类别选择弹窗
    showCategorySelect() {
      this.categorySelectShow = true;
    },
    getdeliveryWayText() {
      return this.deliveryWayList.find(i => i.value == this.formData.deliveryWay)?.label
    },
    onDeliveryWayConfirm(e) {
      const item = e[0];
      if (item) {
        this.formData.deliveryWay = item.value;
      }
      this.deliveryWayShow = false;
    },
    // 获取商品类别列表
    async getCategoryList() {
      try {
        const res = await this.$request({
          url: '/api-merchant/rest/admin/merchantCategory/selectByExample',
          method: 'POST',
          data: {
            isEnabled: 1,
            pageNo: -1,
            pageSize: 20
          }
        });
        if (res.code === 200) {
          this.categoryList = (res.data.records || []).map(item => {
            return {
              id: item.id,
              name: item.categoryName,
            }
          });
        }
      } catch (error) {
        console.error('获取商品类别失败:', error);
      }
    },
    // 确认选择类别
    onCategoryConfirm(selectedIds) {
      this.selectedCategoryIds = selectedIds;
      // 获取选中类别的名称
      this.selectedCategoryNames = this.categoryList
        .filter(item => selectedIds.includes(item.id.toString()))
        .map(item => item.name);
      
      // 更新表单数据中的managePrimary字段
      this.formData.managePrimary = this.selectedCategoryIds.join(',');
      this.categorySelectShow = false;
    },
    // 获取选中的类别名称
    getSelectedCategoryNames() {
      return this.selectedCategoryNames.join(',') || '';
    },
    getImgUrl (refName) {
      const list = this.$refs[refName].lists.filter(val => {
        return val.progress == 100;
      })
      return list.map(item => (item.response?.data || item.url).replace('https://shop-beingwell.oss-cn-hangzhou.aliyuncs.com/', ''))?.join();
    },
    submit () {
      console.log('submit', this.formData.managePrimary);
      this.$refs.uForm.validate().then(async (validate) => {
        if (validate) {
          // debugger
          // const shopLogoImgFile = this.getImgUrl('shopLogoImgFile');
          // if (!shopLogoImgFile) {
          //   uni.$u.toast('门店头像不能为空')
          //   return
          // }

          // const shopWithinImgFile = this.getImgUrl('shopWithinImgFile');
          // if (!shopWithinImgFile) {
          //   uni.$u.toast('店内照片不能为空')
          //   return
          // }

          const businessLicenseFile = this.getImgUrl('businessLicenseFile');
          if (!businessLicenseFile) {
            uni.$u.toast('营业执照不能为空')
            return
          }

          const idCardFrontSideFile = this.getImgUrl('idCardFrontSideFile');
          if (!idCardFrontSideFile) {
            uni.$u.toast('身份证正面不能为空')
            return
          }

          const idCardBackSideFile = this.getImgUrl('idCardBackSideFile');
          if (!idCardBackSideFile) {
            uni.$u.toast('身份证反面不能为空')
            return
          }
          const url = this.formData.auditStatus == 2 ? '/api-merchant/rest/merchant/shop/applyChangeImportantData' : '/api-merchant/rest/merchant/shop/apply';

          const res = await this.$request({
            url: url,
            method: 'POST',
            data: {
              // ...this.formData,
              name: this.formData.name,
              contactRealname: this.formData.contactRealname,
              contactPhone: this.formData.contactPhone,
              province: this.formData.province,
              city: this.formData.city,
              area: this.formData.area,
              street: this.formData.street,
              longitude: this.formData.longitude,
              latitude: this.formData.latitude,
              managePrimary: this.formData.managePrimary,
              shopId: this.formData.id,
              houseNumber: this.formData.houseNumber,
              // shopLogoImg: shopLogoImgFile,
              // shopWithinImg: shopWithinImgFile,
              businessLicense: businessLicenseFile,
              idCardFrontSide: idCardFrontSideFile,
              idCardBackSide: idCardBackSideFile
            }
          });

          if (res.code === 200) {
            uni.showToast({ title: '保存成功', icon: 'success' });
            uni.reLaunch({url: '/pages/index/index'});
          } else {
            uni.showToast({ title: res.message || '保存失败', icon: 'none' });
          }
        }

      })

    },
    // 选择位置
    selectLocation() {
      uni.navigateTo({
        url: '/pages/myInfo/selectLocation'
      });
    },
    
    // 处理位置选择结果
    onLocationSelected(location) {
      if (location) {
        this.formData.street = location.street;
        this.formData.province = location.province;
        this.formData.city = location.city;
        this.formData.area = location.district;
        // 可以保存经纬度信息
        this.formData.longitude = location.lng;
        this.formData.latitude = location.lat;
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.perfectInfo {
  padding: 0 32rpx 32rpx;
}
</style>